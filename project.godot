; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="Outpost Assault"
run/main_scene="res://ui/main_menu.tscn"
config/features=PackedStringArray("4.4", "Forward Plus")
config/icon="res://icon.svg"

[autoload]

Global="*res://autoloads/global.gd"

[display]

window/size/viewport_width=720
window/size/viewport_height=1280
window/size/initial_position=Vector2i(0, 50)
window/size/resizable=false
window/stretch/mode="canvas_items"
window/stretch/aspect="expand"
window/handheld/orientation=1

[gui]

theme/custom="res://ui/ui_theme.tres"

[input]

ui_touch_tap={
"deadzone": 0.5,
"events": [Object(InputEventScreenTouch,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"index":0,"position":Vector2(0, 0),"canceled":false,"pressed":false,"double_tap":false,"script":null)
]
}

[input_devices]

pointing/emulate_touch_from_mouse=true

[layer_names]

2d_physics/layer_1="infantry"
2d_navigation/layer_1="ground"
2d_physics/layer_2="tower"
2d_navigation/layer_2="air"
2d_physics/layer_3="projectile"
2d_physics/layer_4="tower_slot"
2d_physics/layer_5="objective"
2d_physics/layer_6="tank"
2d_physics/layer_7="helicopter"
avoidance/layer_1="ground"
avoidance/layer_2="air"

[rendering]

textures/canvas_textures/default_texture_filter=0
renderer/rendering_method="mobile"
