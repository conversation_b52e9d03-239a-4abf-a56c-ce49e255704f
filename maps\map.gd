extends Node2D

@export var starting_money := 5000
@export var generate_random_map := true
@export var map_width := 20  # Mobile-friendly: narrower width
@export var map_height := 30  # Mobile-friendly: taller height (portrait orientation)
@export var num_spawn_locations := 3
@export var num_tower_slots := 8

@onready var tilemap := $TileMap as TileMap
@onready var camera := $Camera2D as Camera2D
@onready var objective := $Objective as Objective
@onready var spawner := $Spawner as Spawner

# Tile types
enum TileType {
	GRASS = 0,
	PATH = 1,
	WATER = 2
}

# Map generation variables
var map_grid: Array[Array]
var path_tiles: Array[Vector2i]
var spawn_positions: Array[Vector2]
var objective_position: Vector2
var tower_slot_positions: Array[Vector2]

func _ready():
	if generate_random_map:
		generate_random_map_layout()

	# initialize camera
	var map_limits := tilemap.get_used_rect()
	var tile_size := tilemap.tile_set.tile_size
	camera.limit_left = map_limits.position.x * tile_size.x
	camera.limit_top = map_limits.position.y * tile_size.y
	camera.limit_right = map_limits.end.x * tile_size.x
	camera.limit_bottom = map_limits.end.y * tile_size.y
	# initialize money and connect signals
	var hud = camera.hud as HUD
	Global.money_changed.connect(hud._on_money_changed)
	Global.money = starting_money
	hud.initialize(objective.health)
	objective.health_changed.connect(hud._on_objective_health_changed)
	objective.objective_destroyed.connect(_on_objective_destroyed)
	spawner.countdown_started.connect(hud._on_spawner_countdown_started)
	spawner.wave_started.connect(hud._on_spawner_wave_started)
	spawner.enemy_spawned.connect(_on_enemy_spawned)
	spawner.enemies_defeated.connect(_on_enemies_defeated)

	
func _on_enemy_spawned(enemy: Enemy):
	enemy.enemy_died.connect(_on_enemy_died)


func _on_enemy_died(enemy: Enemy):
	Global.money += enemy.kill_reward


func _game_over():
	var hud = camera.hud as HUD
	hud.get_node("Menus/GameOver").enable()
	# Prevent pausing during game over screen
	hud.get_node("Menus/Pause").queue_free()


func _on_objective_destroyed():
	_game_over()


func _on_enemies_defeated():
	_game_over()


func generate_random_map_layout():
	"""
	RANDOM MAP GENERATION RULES:

	1. CORE REQUIREMENTS:
	   - Spawn locations MUST be placed on pathway tiles
	   - Units MUST be able to move smoothly to the objective via pathfinding
	   - All pathway connections MUST be orthogonal (up/down/left/right), NO diagonal-only connections

	2. CORRIDOR-STYLE PATH GENERATION:
	   - Create appealing corridor layouts with frequent turns
	   - After each turn, there MUST be at least 2 straight pathway tiles before another turn
	   - Use more pathways to create interesting corridor designs
	   - Path tiles must be connected orthogonally (adjacent horizontally or vertically)
	   - Main path goes from top (spawns) to bottom (objective) in mobile portrait orientation

	3. ENHANCED CORRIDOR FEATURES:
	   - Add branch corridors that create dead-ends and alternative routes
	   - Create small rooms (2x2 or 3x2) along the path for visual interest
	   - Branch paths also follow the 2-step rule before turning
	   - All corridor segments maintain orthogonal connectivity

	4. SPAWN LOCATIONS:
	   - Must be placed on pathway tiles (never on grass/obstacles)
	   - Should be at the top of the map (spawn side)
	   - Multiple spawn locations allowed for variety

	5. OBJECTIVE PLACEMENT:
	   - Must be placed on a pathway tile
	   - Should be at the bottom of the map (opposite side from spawns)
	   - Must be reachable from all spawn locations

	6. TOWER SLOTS:
	   - Placed on grass tiles adjacent to pathway tiles
	   - Should provide strategic positioning options around corridors
	   - Must not block the main path or corridor access

	6. VALIDATION:
	   - Verify all spawn locations are on path tiles
	   - Verify objective is on a path tile
	   - Verify orthogonal connectivity of all path tiles
	   - Regenerate if validation fails (up to 3 attempts)
	"""

	print("Generating random map...")

	# Initialize grid
	map_grid = []
	for x in range(map_width):
		map_grid.append([])
		for y in range(map_height):
			map_grid[x].append(TileType.GRASS)

	# Generate main path from top to bottom (mobile-friendly orientation)
	generate_main_path()

	# Add some branching paths
	add_branch_paths()

	# Add corridor features (small rooms, wider sections)
	add_corridor_features()

	# Place objective at the end of the main path
	place_objective()

	# Generate spawn locations at path starts
	generate_spawn_locations()

	# Place tower slots strategically
	place_tower_slots()

	# Validate and regenerate if necessary
	regenerate_if_invalid()

	# Apply the generated map to the tilemap
	apply_map_to_tilemap()

	# Update positions of game objects
	update_game_object_positions()


func generate_main_path():
	"""
	NEW CORRIDOR GENERATION RULES:
	1. Create more appealing corridors with frequent turns
	2. After each turn, there must be at least 2 straight pathway tiles before another turn
	3. Use more pathways to create interesting corridor layouts
	4. Maintain orthogonal connections only
	"""
	path_tiles.clear()

	# Start from top side, middle width (mobile portrait: spawns at top, objective at bottom)
	var start_x = clamp(map_width / 2, 1, map_width - 2)
	var current_pos = Vector2i(start_x, 0)
	path_tiles.append(current_pos)
	map_grid[current_pos.x][current_pos.y] = TileType.PATH

	# Track current direction and steps since last turn
	var current_direction = Vector2i(0, 1)  # Start moving down
	var steps_since_turn = 0
	var min_steps_before_turn = 2  # Rule: at least 2 straight steps after a turn

	# Create a winding corridor path with more turns
	while current_pos.y < map_height - 1:
		# Move in current direction
		var next_pos = current_pos + current_direction

		# Check if we can continue in current direction
		var can_continue = (next_pos.x >= 1 and next_pos.x < map_width - 1 and
						   next_pos.y >= 0 and next_pos.y < map_height - 1)

		# Decide if we should turn (only if we've moved at least min_steps_before_turn)
		var should_turn = false
		if steps_since_turn >= min_steps_before_turn:
			# Higher chance to turn if we're getting close to edges
			var edge_pressure = 0.0
			if next_pos.x <= 2 or next_pos.x >= map_width - 3:
				edge_pressure += 0.4
			if next_pos.y >= map_height - 5:
				edge_pressure += 0.2

			# Base turn chance + edge pressure
			should_turn = randf() < (0.3 + edge_pressure)

		# Force turn if we can't continue or hit boundary
		if not can_continue or next_pos.y >= map_height - 1:
			should_turn = true

		if should_turn and steps_since_turn >= min_steps_before_turn:
			# Choose new direction (perpendicular to current)
			var possible_directions: Array[Vector2i] = []

			if current_direction.y != 0:  # Currently moving vertically
				# Can turn left or right
				if current_pos.x > 2:
					possible_directions.append(Vector2i(-1, 0))  # Left
				if current_pos.x < map_width - 3:
					possible_directions.append(Vector2i(1, 0))   # Right
				# Can also continue down if possible
				if current_pos.y < map_height - 3:
					possible_directions.append(Vector2i(0, 1))   # Down
			else:  # Currently moving horizontally
				# Must turn down (we're going from top to bottom)
				if current_pos.y < map_height - 2:
					possible_directions.append(Vector2i(0, 1))   # Down

			# Choose random direction from possible ones
			if possible_directions.size() > 0:
				var old_direction = current_direction
				current_direction = possible_directions[randi() % possible_directions.size()]

				# Only reset step counter if direction actually changed
				if current_direction != old_direction:
					steps_since_turn = 0
					print("Turn at ", current_pos, " from ", old_direction, " to ", current_direction)

		# Move in the (possibly new) direction
		next_pos = current_pos + current_direction

		# Final boundary check
		if next_pos.x < 1 or next_pos.x >= map_width - 1 or next_pos.y < 0 or next_pos.y >= map_height:
			# Force move down if we hit a boundary
			if current_pos.y < map_height - 1:
				current_direction = Vector2i(0, 1)
				next_pos = current_pos + current_direction
				steps_since_turn = 0
			else:
				break  # Reached bottom

		# Add the tile
		path_tiles.append(next_pos)
		map_grid[next_pos.x][next_pos.y] = TileType.PATH
		current_pos = next_pos
		steps_since_turn += 1


func add_branch_paths():
	"""
	Create additional corridor segments to make the path more interesting
	These create dead-ends and alternative routes that add visual complexity
	"""
	var num_branches = randi_range(2, 4)  # More branches for interesting corridors

	for i in range(num_branches):
		if path_tiles.size() < 8:
			continue

		# Pick a random point on the main path (avoid start/end areas)
		var branch_start_idx = randi_range(4, path_tiles.size() - 6)
		var branch_start = path_tiles[branch_start_idx]

		# Create corridor segments (3-5 tiles) with the 2-step rule
		var branch_length = randi_range(3, 5)
		var current_pos = branch_start

		# Choose initial direction perpendicular to likely main path direction
		var possible_dirs = [Vector2i(1, 0), Vector2i(-1, 0)]  # Horizontal branches
		var branch_dir = possible_dirs[randi() % possible_dirs.size()]

		var steps_in_direction = 0
		var min_steps_before_branch_turn = 2

		# Generate branch corridor with turn rules
		for j in range(branch_length):
			var next_pos = current_pos + branch_dir

			# Check bounds and existing paths
			if (next_pos.x < 1 or next_pos.x >= map_width - 1 or
				next_pos.y < 1 or next_pos.y >= map_height - 1 or
				map_grid[next_pos.x][next_pos.y] == TileType.PATH):

				# Try to turn if we've moved enough steps
				if steps_in_direction >= min_steps_before_branch_turn:
					var turn_directions = [Vector2i(0, 1), Vector2i(0, -1)]  # Vertical
					var found_valid_turn = false

					for turn_dir in turn_directions:
						var turn_pos = current_pos + turn_dir
						if (turn_pos.x >= 1 and turn_pos.x < map_width - 1 and
							turn_pos.y >= 1 and turn_pos.y < map_height - 1 and
							map_grid[turn_pos.x][turn_pos.y] != TileType.PATH):
							branch_dir = turn_dir
							next_pos = turn_pos
							steps_in_direction = 0
							found_valid_turn = true
							break

					if not found_valid_turn:
						break  # Can't continue branch
				else:
					break  # Can't continue and haven't moved enough to turn

			# Add the branch tile
			path_tiles.append(next_pos)
			map_grid[next_pos.x][next_pos.y] = TileType.PATH
			current_pos = next_pos
			steps_in_direction += 1

			# Consider turning after minimum steps (create L-shaped corridors)
			if steps_in_direction >= min_steps_before_branch_turn and randf() < 0.4:
				var turn_directions = []
				if branch_dir.x != 0:  # Moving horizontally, can turn vertically
					turn_directions = [Vector2i(0, 1), Vector2i(0, -1)]
				else:  # Moving vertically, can turn horizontally
					turn_directions = [Vector2i(1, 0), Vector2i(-1, 0)]

				# Try to turn
				for turn_dir in turn_directions:
					var turn_pos = current_pos + turn_dir
					if (turn_pos.x >= 1 and turn_pos.x < map_width - 1 and
						turn_pos.y >= 1 and turn_pos.y < map_height - 1 and
						map_grid[turn_pos.x][turn_pos.y] != TileType.PATH):
						branch_dir = turn_dir
						steps_in_direction = 0
						print("Branch turn at ", current_pos, " to direction ", turn_dir)
						break


func add_corridor_features():
	"""
	Add small rooms and wider corridor sections to make the level more interesting
	"""
	var num_features = randi_range(1, 3)

	for i in range(num_features):
		if path_tiles.size() < 10:
			continue

		# Pick a random path tile to expand around
		var center_idx = randi_range(5, path_tiles.size() - 6)
		var center_tile = path_tiles[center_idx]

		# Create a small room (2x2 or 3x2) around this tile
		var room_width = randi_range(2, 3)
		var room_height = randi_range(2, 3)

		# Calculate room bounds
		var start_x = center_tile.x - room_width / 2
		var start_y = center_tile.y - room_height / 2
		var end_x = start_x + room_width
		var end_y = start_y + room_height

		# Check if room fits within bounds
		if (start_x >= 1 and end_x < map_width - 1 and
			start_y >= 1 and end_y < map_height - 1):

			# Add room tiles
			for x in range(start_x, end_x + 1):
				for y in range(start_y, end_y + 1):
					var room_tile = Vector2i(x, y)
					if not path_tiles.has(room_tile):
						path_tiles.append(room_tile)
						map_grid[x][y] = TileType.PATH

			print("Added corridor feature (room) at ", center_tile, " size ", room_width, "x", room_height)


func place_objective():
	# Place objective at the bottommost path tile (mobile portrait: objective at bottom)
	var bottommost_tiles: Array[Vector2i] = []
	var bottommost_y = -1

	# Find the bottommost y coordinate
	for tile in path_tiles:
		if tile.y > bottommost_y:
			bottommost_y = tile.y

	# Collect all tiles at the bottommost y coordinate
	for tile in path_tiles:
		if tile.y == bottommost_y:
			bottommost_tiles.append(tile)

	# Choose a random tile from the bottommost tiles
	if bottommost_tiles.size() > 0:
		var objective_tile = bottommost_tiles[randi() % bottommost_tiles.size()]
		objective_position = Vector2(objective_tile.x * 128 + 64, objective_tile.y * 128 + 64)
	else:
		# Fallback to last tile in path
		var objective_tile = path_tiles[-1]
		objective_position = Vector2(objective_tile.x * 128 + 64, objective_tile.y * 128 + 64)


func generate_spawn_locations():
	spawn_positions.clear()

	# Find topmost path tiles for spawn locations (mobile portrait: spawns at top)
	var topmost_tiles: Array[Vector2i] = []
	var topmost_y = map_height

	# Find the topmost y coordinate
	for tile in path_tiles:
		if tile.y < topmost_y:
			topmost_y = tile.y

	# Collect all tiles at the topmost y coordinate and nearby
	for tile in path_tiles:
		if tile.y <= topmost_y + 1:  # Include tiles from topmost and one row down
			topmost_tiles.append(tile)

	# Ensure we have enough tiles for spawn locations
	if topmost_tiles.size() < num_spawn_locations:
		# Add more tiles from the beginning of the path if needed
		for tile in path_tiles:
			if tile.y <= topmost_y + 2 and not topmost_tiles.has(tile):
				topmost_tiles.append(tile)
				if topmost_tiles.size() >= num_spawn_locations:
					break

	# Create spawn positions from these tiles (ensure they're on path)
	topmost_tiles.shuffle()  # Randomize selection
	for i in range(min(num_spawn_locations, topmost_tiles.size())):
		var tile = topmost_tiles[i]
		spawn_positions.append(Vector2(tile.x * 128 + 64, tile.y * 128 + 64))


func place_tower_slots():
	tower_slot_positions.clear()

	# Place tower slots on grass tiles adjacent to path tiles
	var potential_slots: Array[Vector2i] = []

	for tile in path_tiles:
		# Check all 8 directions around each path tile
		for dx in range(-1, 2):
			for dy in range(-1, 2):
				if dx == 0 and dy == 0:
					continue

				var check_pos = Vector2i(tile.x + dx, tile.y + dy)

				# Check bounds
				if check_pos.x < 0 or check_pos.x >= map_width or check_pos.y < 0 or check_pos.y >= map_height:
					continue

				# Only place on grass tiles
				if map_grid[check_pos.x][check_pos.y] == TileType.GRASS:
					if not potential_slots.has(check_pos):
						potential_slots.append(check_pos)

	# Randomly select tower slot positions
	potential_slots.shuffle()
	for i in range(min(num_tower_slots, potential_slots.size())):
		var tile = potential_slots[i]
		tower_slot_positions.append(Vector2(tile.x * 128 + 64, tile.y * 128 + 64))


func apply_map_to_tilemap():
	# Clear existing tiles
	tilemap.clear()

	print("Applying generated map to tilemap...")

	# Apply the generated map
	for x in range(map_width):
		for y in range(map_height):
			var tile_type = map_grid[x][y]
			var source_id = 0
			var atlas_coords = Vector2i.ZERO

			match tile_type:
				TileType.GRASS:
					atlas_coords = Vector2i(0, 3)  # Grass tile
				TileType.PATH:
					atlas_coords = Vector2i(0, 0)  # Path tile with navigation
				TileType.WATER:
					atlas_coords = Vector2i(1, 3)  # Water tile

			tilemap.set_cell(0, Vector2i(x, y), source_id, atlas_coords)

	print("Tilemap updated with ", map_width * map_height, " tiles")


func update_game_object_positions():
	# Update objective position
	if objective:
		objective.position = objective_position
		# Add objective to a group for easy finding
		if not objective.is_in_group("objective"):
			objective.add_to_group("objective")
		print("Objective placed at: ", objective_position)

	# Add map to group for easy finding
	if not is_in_group("map"):
		add_to_group("map")

	# Update spawn locations
	if spawner:
		update_spawn_locations()

	# Update tower slots
	update_tower_slots()

	print("Map generation complete!")
	print("- Path tiles: ", path_tiles.size())
	print("- Spawn locations: ", spawn_positions.size())
	print("- Tower slots: ", tower_slot_positions.size())

	# Ensure navigation is ready before allowing spawning
	call_deferred("_finalize_map_setup")


func update_spawn_locations():
	if not spawner:
		print("Warning: Spawner not found")
		return

	var spawn_container = spawner.get_node("SpawnContainer")
	if not spawn_container:
		print("Warning: SpawnContainer not found")
		return

	# Clear existing spawn locations
	for child in spawn_container.get_children():
		child.queue_free()

	# Wait a frame for the queue_free to take effect
	await get_tree().process_frame

	# Create new spawn locations
	for i in range(spawn_positions.size()):
		var new_spawn = Marker2D.new()
		new_spawn.name = "SpawnLocation" + str(i + 1)
		new_spawn.position = spawn_positions[i]
		spawn_container.add_child(new_spawn)
		print("Spawn location ", i + 1, " placed at: ", spawn_positions[i])

	# Update spawner's spawn_locations array
	spawner.spawn_locations.clear()
	for marker in spawn_container.get_children():
		spawner.spawn_locations.append(marker)


func _finalize_map_setup():
	# Ensure navigation mesh is updated
	if has_node("AircraftNavRegion"):
		var nav_region = get_node("AircraftNavRegion")
		nav_region.bake_navigation_polygon()

	# Wait a bit more to ensure everything is ready
	await get_tree().create_timer(0.1).timeout

	print("Map setup finalized - ready for gameplay!")


func update_tower_slots():
	# Remove existing tower slots
	var existing_slots = get_children().filter(func(child): return child.name.begins_with("TowerSlot"))
	for slot in existing_slots:
		slot.queue_free()

	# Create new tower slots
	var tower_slot_scene = preload("res://entities/towers/tower_slot.tscn")

	for i in range(tower_slot_positions.size()):
		var tower_slot = tower_slot_scene.instantiate()
		tower_slot.name = "TowerSlot" + str(i + 1)
		tower_slot.position = tower_slot_positions[i]
		add_child(tower_slot)


func get_random_spawn_position() -> Vector2:
	if spawn_positions.size() > 0:
		return spawn_positions[randi() % spawn_positions.size()]
	return Vector2.ZERO


func is_position_on_path(world_pos: Vector2) -> bool:
	var tile_pos = Vector2i(int(world_pos.x / 128), int(world_pos.y / 128))
	return path_tiles.has(tile_pos)


func validate_map_connectivity() -> bool:
	# Ensure all spawn locations are on path tiles
	for spawn_pos in spawn_positions:
		if not is_position_on_path(spawn_pos):
			print("Warning: Spawn location not on path: ", spawn_pos)
			return false

	# Ensure objective is reachable from spawn locations
	if not is_position_on_path(objective_position):
		print("Warning: Objective not on path: ", objective_position)
		return false

	# Basic connectivity check - ensure path is continuous
	if path_tiles.size() < 2:
		print("Warning: Path too short")
		return false

	# Validate orthogonal connectivity
	if not validate_orthogonal_connectivity():
		print("Warning: Path has diagonal-only connections")
		return false

	print("Map connectivity validated successfully")
	return true


func validate_orthogonal_connectivity() -> bool:
	# Check that each path tile (except the first) is orthogonally connected to at least one other path tile
	for i in range(path_tiles.size()):
		var tile = path_tiles[i]
		var has_orthogonal_neighbor = false

		# Check all 4 orthogonal directions
		var orthogonal_dirs = [Vector2i(0, 1), Vector2i(0, -1), Vector2i(1, 0), Vector2i(-1, 0)]
		for dir in orthogonal_dirs:
			var neighbor = tile + dir
			if path_tiles.has(neighbor):
				has_orthogonal_neighbor = true
				break

		# First and last tiles only need one connection, middle tiles should have at least one
		if not has_orthogonal_neighbor:
			print("Warning: Path tile at ", tile, " has no orthogonal neighbors")
			return false

	return true


func regenerate_if_invalid():
	# If map validation fails, try regenerating up to 3 times
	var attempts = 0
	while attempts < 3:
		if validate_map_connectivity():
			break

		attempts += 1
		print("Map validation failed, regenerating... (attempt ", attempts, ")")

		# Clear and regenerate
		path_tiles.clear()
		spawn_positions.clear()
		tower_slot_positions.clear()

		generate_main_path()
		add_branch_paths()
		place_objective()
		generate_spawn_locations()
		place_tower_slots()

	if attempts >= 3:
		print("Warning: Could not generate valid map after 3 attempts, using current map")


func regenerate_map():
	"""Call this function to regenerate the map at runtime"""
	if generate_random_map:
		generate_random_map_layout()
	else:
		print("Random map generation is disabled")


func _input(event):
	# Allow regenerating map with R key for testing
	if event.is_action_pressed("ui_accept") and Input.is_action_pressed("ui_cancel"):
		print("Regenerating map...")
		regenerate_map()
