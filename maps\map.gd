extends Node2D

@export var starting_money := 5000
@export var generate_random_map := true
@export var map_width := 30
@export var map_height := 20
@export var num_spawn_locations := 3
@export var num_tower_slots := 8

@onready var tilemap := $TileMap as TileMap
@onready var camera := $Camera2D as Camera2D
@onready var objective := $Objective as Objective
@onready var spawner := $Spawner as Spawner

# Tile types
enum TileType {
	GRASS = 0,
	PATH = 1,
	WATER = 2
}

# Map generation variables
var map_grid: Array[Array]
var path_tiles: Array[Vector2i]
var spawn_positions: Array[Vector2]
var objective_position: Vector2
var tower_slot_positions: Array[Vector2]

func _ready():
	if generate_random_map:
		generate_random_map_layout()

	# initialize camera
	var map_limits := tilemap.get_used_rect()
	var tile_size := tilemap.tile_set.tile_size
	camera.limit_left = map_limits.position.x * tile_size.x
	camera.limit_top = map_limits.position.y * tile_size.y
	camera.limit_right = map_limits.end.x * tile_size.x
	camera.limit_bottom = map_limits.end.y * tile_size.y
	# initialize money and connect signals
	var hud = camera.hud as HUD
	Global.money_changed.connect(hud._on_money_changed)
	Global.money = starting_money
	hud.initialize(objective.health)
	objective.health_changed.connect(hud._on_objective_health_changed)
	objective.objective_destroyed.connect(_on_objective_destroyed)
	spawner.countdown_started.connect(hud._on_spawner_countdown_started)
	spawner.wave_started.connect(hud._on_spawner_wave_started)
	spawner.enemy_spawned.connect(_on_enemy_spawned)
	spawner.enemies_defeated.connect(_on_enemies_defeated)

	
func _on_enemy_spawned(enemy: Enemy):
	enemy.enemy_died.connect(_on_enemy_died)


func _on_enemy_died(enemy: Enemy):
	Global.money += enemy.kill_reward


func _game_over():
	var hud = camera.hud as HUD
	hud.get_node("Menus/GameOver").enable()
	# Prevent pausing during game over screen
	hud.get_node("Menus/Pause").queue_free()


func _on_objective_destroyed():
	_game_over()


func _on_enemies_defeated():
	_game_over()


func generate_random_map_layout():
	print("Generating random map...")

	# Initialize grid
	map_grid = []
	for x in range(map_width):
		map_grid.append([])
		for y in range(map_height):
			map_grid[x].append(TileType.GRASS)

	# Generate main path from left to right
	generate_main_path()

	# Add some branching paths
	add_branch_paths()

	# Place objective at the end of the main path
	place_objective()

	# Generate spawn locations at path starts
	generate_spawn_locations()

	# Place tower slots strategically
	place_tower_slots()

	# Validate and regenerate if necessary
	regenerate_if_invalid()

	# Apply the generated map to the tilemap
	apply_map_to_tilemap()

	# Update positions of game objects
	update_game_object_positions()


func generate_main_path():
	path_tiles.clear()

	# Start from left side, middle height
	var start_y = clamp(map_height / 2, 1, map_height - 2)
	var current_pos = Vector2i(0, start_y)
	path_tiles.append(current_pos)
	map_grid[current_pos.x][current_pos.y] = TileType.PATH

	# Create a winding path to the right side
	while current_pos.x < map_width - 1:
		var next_pos = current_pos

		# Always move right
		next_pos.x += 1

		# Randomly move up or down occasionally, but keep within bounds
		if randf() < 0.3:  # 30% chance to change vertical direction
			var vertical_change = randi_range(-1, 1)
			next_pos.y = clamp(next_pos.y + vertical_change, 1, map_height - 2)

		# Ensure we don't go out of bounds
		next_pos.y = clamp(next_pos.y, 1, map_height - 2)

		path_tiles.append(next_pos)
		map_grid[next_pos.x][next_pos.y] = TileType.PATH
		current_pos = next_pos


func add_branch_paths():
	# Add some short branch paths for variety
	var num_branches = randi_range(2, 4)

	for i in range(num_branches):
		if path_tiles.size() < 4:
			continue

		# Pick a random point on the main path (not too close to start/end)
		var branch_start_idx = randi_range(2, path_tiles.size() - 3)
		var branch_start = path_tiles[branch_start_idx]

		# Create a short branch (2-4 tiles)
		var branch_length = randi_range(2, 4)
		var current_pos = branch_start

		# Choose random direction for branch
		var directions = [Vector2i(0, 1), Vector2i(0, -1), Vector2i(1, 0), Vector2i(-1, 0)]
		var branch_dir = directions[randi() % directions.size()]

		for j in range(branch_length):
			var next_pos = current_pos + branch_dir

			# Check bounds
			if next_pos.x < 0 or next_pos.x >= map_width or next_pos.y < 0 or next_pos.y >= map_height:
				break

			# Don't overwrite existing path
			if map_grid[next_pos.x][next_pos.y] == TileType.PATH:
				break

			path_tiles.append(next_pos)
			map_grid[next_pos.x][next_pos.y] = TileType.PATH
			current_pos = next_pos


func place_objective():
	# Place objective at the rightmost path tile
	var rightmost_tiles: Array[Vector2i] = []
	var rightmost_x = -1

	# Find the rightmost x coordinate
	for tile in path_tiles:
		if tile.x > rightmost_x:
			rightmost_x = tile.x

	# Collect all tiles at the rightmost x coordinate
	for tile in path_tiles:
		if tile.x == rightmost_x:
			rightmost_tiles.append(tile)

	# Choose a random tile from the rightmost tiles
	if rightmost_tiles.size() > 0:
		var objective_tile = rightmost_tiles[randi() % rightmost_tiles.size()]
		objective_position = Vector2(objective_tile.x * 128 + 64, objective_tile.y * 128 + 64)
	else:
		# Fallback to last tile in path
		var objective_tile = path_tiles[-1]
		objective_position = Vector2(objective_tile.x * 128 + 64, objective_tile.y * 128 + 64)


func generate_spawn_locations():
	spawn_positions.clear()

	# Find leftmost path tiles for spawn locations
	var leftmost_tiles: Array[Vector2i] = []
	var leftmost_x = map_width

	# Find the leftmost x coordinate
	for tile in path_tiles:
		if tile.x < leftmost_x:
			leftmost_x = tile.x

	# Collect all tiles at the leftmost x coordinate and nearby
	for tile in path_tiles:
		if tile.x <= leftmost_x + 1:  # Include tiles from leftmost and one column to the right
			leftmost_tiles.append(tile)

	# Ensure we have enough tiles for spawn locations
	if leftmost_tiles.size() < num_spawn_locations:
		# Add more tiles from the beginning of the path if needed
		for tile in path_tiles:
			if tile.x <= leftmost_x + 2 and not leftmost_tiles.has(tile):
				leftmost_tiles.append(tile)
				if leftmost_tiles.size() >= num_spawn_locations:
					break

	# Create spawn positions from these tiles (ensure they're on path)
	leftmost_tiles.shuffle()  # Randomize selection
	for i in range(min(num_spawn_locations, leftmost_tiles.size())):
		var tile = leftmost_tiles[i]
		spawn_positions.append(Vector2(tile.x * 128 + 64, tile.y * 128 + 64))


func place_tower_slots():
	tower_slot_positions.clear()

	# Place tower slots on grass tiles adjacent to path tiles
	var potential_slots: Array[Vector2i] = []

	for tile in path_tiles:
		# Check all 8 directions around each path tile
		for dx in range(-1, 2):
			for dy in range(-1, 2):
				if dx == 0 and dy == 0:
					continue

				var check_pos = Vector2i(tile.x + dx, tile.y + dy)

				# Check bounds
				if check_pos.x < 0 or check_pos.x >= map_width or check_pos.y < 0 or check_pos.y >= map_height:
					continue

				# Only place on grass tiles
				if map_grid[check_pos.x][check_pos.y] == TileType.GRASS:
					if not potential_slots.has(check_pos):
						potential_slots.append(check_pos)

	# Randomly select tower slot positions
	potential_slots.shuffle()
	for i in range(min(num_tower_slots, potential_slots.size())):
		var tile = potential_slots[i]
		tower_slot_positions.append(Vector2(tile.x * 128 + 64, tile.y * 128 + 64))


func apply_map_to_tilemap():
	# Clear existing tiles
	tilemap.clear()

	print("Applying generated map to tilemap...")

	# Apply the generated map
	for x in range(map_width):
		for y in range(map_height):
			var tile_type = map_grid[x][y]
			var source_id = 0
			var atlas_coords = Vector2i.ZERO

			match tile_type:
				TileType.GRASS:
					atlas_coords = Vector2i(0, 3)  # Grass tile
				TileType.PATH:
					atlas_coords = Vector2i(0, 0)  # Path tile with navigation
				TileType.WATER:
					atlas_coords = Vector2i(1, 3)  # Water tile

			tilemap.set_cell(0, Vector2i(x, y), source_id, atlas_coords)

	print("Tilemap updated with ", map_width * map_height, " tiles")


func update_game_object_positions():
	# Update objective position
	if objective:
		objective.position = objective_position
		print("Objective placed at: ", objective_position)

	# Update spawn locations
	if spawner:
		update_spawn_locations()

	# Update tower slots
	update_tower_slots()

	print("Map generation complete!")
	print("- Path tiles: ", path_tiles.size())
	print("- Spawn locations: ", spawn_positions.size())
	print("- Tower slots: ", tower_slot_positions.size())


func update_spawn_locations():
	var spawn_container = spawner.get_node("SpawnContainer")

	# Clear existing spawn locations
	for child in spawn_container.get_children():
		child.queue_free()

	# Create new spawn locations
	for i in range(spawn_positions.size()):
		var new_spawn = Marker2D.new()
		new_spawn.name = "SpawnLocation" + str(i + 1)
		new_spawn.position = spawn_positions[i]
		spawn_container.add_child(new_spawn)
		print("Spawn location ", i + 1, " placed at: ", spawn_positions[i])

	# Update spawner's spawn_locations array
	spawner.spawn_locations.clear()
	for marker in spawn_container.get_children():
		spawner.spawn_locations.append(marker)


func update_tower_slots():
	# Remove existing tower slots
	var existing_slots = get_children().filter(func(child): return child.name.begins_with("TowerSlot"))
	for slot in existing_slots:
		slot.queue_free()

	# Create new tower slots
	var tower_slot_scene = preload("res://entities/towers/tower_slot.tscn")

	for i in range(tower_slot_positions.size()):
		var tower_slot = tower_slot_scene.instantiate()
		tower_slot.name = "TowerSlot" + str(i + 1)
		tower_slot.position = tower_slot_positions[i]
		add_child(tower_slot)


func get_random_spawn_position() -> Vector2:
	if spawn_positions.size() > 0:
		return spawn_positions[randi() % spawn_positions.size()]
	return Vector2.ZERO


func is_position_on_path(world_pos: Vector2) -> bool:
	var tile_pos = Vector2i(int(world_pos.x / 128), int(world_pos.y / 128))
	return path_tiles.has(tile_pos)


func validate_map_connectivity() -> bool:
	# Ensure all spawn locations are on path tiles
	for spawn_pos in spawn_positions:
		if not is_position_on_path(spawn_pos):
			print("Warning: Spawn location not on path: ", spawn_pos)
			return false

	# Ensure objective is reachable from spawn locations
	if not is_position_on_path(objective_position):
		print("Warning: Objective not on path: ", objective_position)
		return false

	# Basic connectivity check - ensure path is continuous
	if path_tiles.size() < 2:
		print("Warning: Path too short")
		return false

	print("Map connectivity validated successfully")
	return true


func regenerate_if_invalid():
	# If map validation fails, try regenerating up to 3 times
	var attempts = 0
	while attempts < 3:
		if validate_map_connectivity():
			break

		attempts += 1
		print("Map validation failed, regenerating... (attempt ", attempts, ")")

		# Clear and regenerate
		path_tiles.clear()
		spawn_positions.clear()
		tower_slot_positions.clear()

		generate_main_path()
		add_branch_paths()
		place_objective()
		generate_spawn_locations()
		place_tower_slots()

	if attempts >= 3:
		print("Warning: Could not generate valid map after 3 attempts, using current map")


func regenerate_map():
	"""Call this function to regenerate the map at runtime"""
	if generate_random_map:
		generate_random_map_layout()
	else:
		print("Random map generation is disabled")


func _input(event):
	# Allow regenerating map with R key for testing
	if event.is_action_pressed("ui_accept") and Input.is_action_pressed("ui_cancel"):
		print("Regenerating map...")
		regenerate_map()
