extends Camera2D

@export var min_zoom := 0.3
@export var max_zoom := 1.2
@export var zoom_rate := 16.0
@export var zoom_delta := 0.15
@export var drag_speed := 1.0

# Mobile touch variables
var is_dragging := false
var last_touch_position := Vector2.ZERO
var touch_start_position := Vector2.ZERO
var pinch_start_distance := 0.0
var pinch_start_zoom := 1.0

@onready var target_zoom: float = zoom.x
@onready var hud := $HUD as HUD

func _physics_process(delta: float) -> void:
	zoom.x = lerp(zoom.x, target_zoom, zoom_rate * delta)
	zoom.y = lerp(zoom.y, target_zoom, zoom_rate * delta)


func _unhandled_input(event: InputEvent) -> void:
	# Handle mouse input (for desktop testing)
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_WHEEL_UP:
			target_zoom = min(target_zoom + zoom_delta, max_zoom)
		if event.button_index == MOUSE_BUTTON_WHEEL_DOWN:
			target_zoom = max(target_zoom - zoom_delta, min_zoom)
		if event.pressed:
			if event.button_index == MOUSE_BUTTON_MIDDLE or event.button_index == MOUSE_BUTTON_LEFT:
				is_dragging = true
				last_touch_position = event.position
				Input.set_default_cursor_shape(Input.CURSOR_DRAG)
		elif event.button_index == MOUSE_BUTTON_MIDDLE or event.button_index == MOUSE_BUTTON_LEFT:
			is_dragging = false
			Input.set_default_cursor_shape(Input.CURSOR_ARROW)

	if event is InputEventMouseMotion and is_dragging:
		var delta_pos = (last_touch_position - event.position) * drag_speed / zoom.x
		position += delta_pos
		last_touch_position = event.position

	# Handle touch input (mobile)
	if event is InputEventScreenTouch:
		if event.pressed:
			if get_viewport().get_screen_touches().size() == 1:
				# Single touch - start dragging
				is_dragging = true
				touch_start_position = event.position
				last_touch_position = event.position
			elif get_viewport().get_screen_touches().size() == 2:
				# Two touches - start pinch zoom
				is_dragging = false
				var touches = get_viewport().get_screen_touches()
				pinch_start_distance = touches[0].distance_to(touches[1])
				pinch_start_zoom = target_zoom
		else:
			is_dragging = false

	if event is InputEventScreenDrag:
		var touches = get_viewport().get_screen_touches()
		if touches.size() == 1 and is_dragging:
			# Single touch drag - pan camera
			var delta_pos = (last_touch_position - event.position) * drag_speed / zoom.x
			position += delta_pos
			last_touch_position = event.position
		elif touches.size() == 2:
			# Two touch drag - pinch zoom
			var current_distance = touches[0].distance_to(touches[1])
			if pinch_start_distance > 0:
				var zoom_factor = current_distance / pinch_start_distance
				target_zoom = clamp(pinch_start_zoom * zoom_factor, min_zoom, max_zoom)
